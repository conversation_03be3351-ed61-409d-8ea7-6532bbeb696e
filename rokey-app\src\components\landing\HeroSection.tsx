'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRightIcon, PlayIcon, SparklesIcon, BoltIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';
import InstantLink from '@/components/ui/InstantLink';

export default function HeroSection() {
  const [activeFeatureCard, setActiveFeatureCard] = useState('routing');

  return (
    <>
      {/* Hero Section - N8N Style Rectangular */}
      <section className="relative h-[95vh] overflow-hidden" style={{
        backgroundImage: 'url(/Hero_Section_Background_Image.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}>
        {/* Subtle Grid Overlay with Glow */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.008)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.008)_1px,transparent_1px)] bg-[size:50px_50px]"
             style={{
               boxShadow: 'inset 0 0 100px rgba(255,255,255,0.02)'
             }} />

        {/* Dark overlay for text readability */}
        <div className="absolute inset-0 bg-black/40"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center h-full py-16">
            {/* Left Column - Content */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4 }}
              className="text-left"
            >
              {/* RouKey Hero Title */}
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
                Universal AI <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">gateway</span>
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">
                  with your own keys
                </span>
              </h1>

              <p className="text-base md:text-lg text-gray-300 mb-8 leading-relaxed max-w-lg">
                Connect to 50+ AI providers using your own API keys. Smart routing,
                fallback protection, and cost optimization. One unified interface
                for OpenAI, Claude, Gemini, and more - all with complete control
                over your data and costs.
              </p>

              {/* N8N-Style CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <InstantLink
                  href="/pricing"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-orange-500/25"
                >
                  Get started for free
                </InstantLink>

                <InstantLink
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 border border-white/20 text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-200"
                >
                  Talk to sales
                </InstantLink>
              </motion.div>
            </motion.div>

            {/* Right Column - Visual Element */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              className="relative flex justify-center items-center mt-64"
            >
              {/* Lightning Bolt Style Visual - Enhanced with Shine & Gloss */}
              <div className="relative">
                {/* Main Lightning Bolt with Enhanced Gradient */}
                <div className="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-400 via-orange-400 via-orange-500 to-orange-600 opacity-95 drop-shadow-2xl">
                  ⚡
                </div>

                {/* Glossy Highlight Overlay - Brighter */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-200 via-orange-300 to-transparent opacity-40">
                  ⚡
                </div>

                {/* Outer Glow - Brighter */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-orange-400/50 blur-2xl">
                  ⚡
                </div>

                {/* Inner Glow - Brighter */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-yellow-400/45 blur-lg">
                  ⚡
                </div>

                {/* Shine Effect - Brighter Animation */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-transparent via-yellow-300 to-transparent opacity-30 animate-pulse">
                  ⚡
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Feature Cards Section - Enhanced Design */}
      <section className="relative py-20" style={{
        background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
      }}>
        {/* Grid overlay to match other sections */}
        <div className="absolute inset-0 opacity-20">
          <div
            style={{
              backgroundImage: `
                linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px'
            }}
          />
        </div>

        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Stop switching between AI tools
            </h2>
            <p className="text-lg text-gray-300 max-w-2xl mx-auto">
              One API call. Any model. Your keys, your control, your costs.
            </p>
          </div>

          {/* Enhanced Feature Cards Container - More Square */}
          <div className="relative max-w-4xl mx-auto">
            {/* Ambient glow background */}
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 via-purple-500/10 to-orange-500/20 rounded-2xl blur-2xl scale-105"></div>

            {/* Main container with glass morphism effect */}
            <div className="relative bg-white/5 backdrop-blur-xl rounded-2xl border border-white/10 shadow-2xl overflow-hidden">
              {/* Subtle inner glow */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5 rounded-3xl"></div>

              {/* Feature Cards Grid */}
              <div className="relative p-4">
                <div className="grid grid-cols-2 md:grid-cols-5 gap-2 mb-4">
                {/* Model Routing Card */}
                <button
                  onClick={() => setActiveFeatureCard('routing')}
                  className={`group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'routing'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'routing' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-1">
                      <div className={`w-5 h-5 rounded flex items-center justify-center mr-2 text-xs ${
                        activeFeatureCard === 'routing'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        🎯
                      </div>
                      <div className="text-xs font-semibold">Model Routing</div>
                    </div>
                    <div className="text-xs text-gray-400 ml-7">
                      Smart selection
                    </div>
                  </div>
                </button>

                {/* Multi-Role Orchestration Card */}
                <button
                  onClick={() => setActiveFeatureCard('orchestration')}
                  className={`group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'orchestration'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'orchestration' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-1">
                      <div className={`w-5 h-5 rounded flex items-center justify-center mr-2 text-xs ${
                        activeFeatureCard === 'orchestration'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        🔗
                      </div>
                      <div className="text-xs font-semibold">Multi-Role</div>
                    </div>
                    <div className="text-xs text-gray-400 ml-7">
                      Chain AI roles
                    </div>
                  </div>
                </button>

                {/* API Usage Card */}
                <button
                  onClick={() => setActiveFeatureCard('api')}
                  className={`group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'api'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'api' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-1">
                      <div className={`w-5 h-5 rounded flex items-center justify-center mr-2 text-xs ${
                        activeFeatureCard === 'api'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        💻
                      </div>
                      <div className="text-xs font-semibold">API Usage</div>
                    </div>
                    <div className="text-xs text-gray-400 ml-7">
                      Real code
                    </div>
                  </div>
                </button>

                {/* Cost Optimization Card */}
                <button
                  onClick={() => setActiveFeatureCard('costs')}
                  className={`group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'costs'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'costs' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-1">
                      <div className={`w-5 h-5 rounded flex items-center justify-center mr-2 text-xs ${
                        activeFeatureCard === 'costs'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        💰
                      </div>
                      <div className="text-xs font-semibold">Cost Savings</div>
                    </div>
                    <div className="text-xs text-gray-400 ml-7">
                      Save money
                    </div>
                  </div>
                </button>

                {/* Demo Video Card */}
                <button
                  onClick={() => setActiveFeatureCard('demo')}
                  className={`group relative p-3 rounded-lg border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'demo'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-lg shadow-orange-500/20 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'demo' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent rounded-lg blur-lg"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-1">
                      <div className={`w-5 h-5 rounded flex items-center justify-center mr-2 text-xs ${
                        activeFeatureCard === 'demo'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        ▶️
                      </div>
                      <div className="text-xs font-semibold">Live Demo</div>
                    </div>
                    <div className="text-xs text-gray-400 ml-7">
                      Watch video
                    </div>
                  </div>
                </button>
              </div>
            </div>

              {/* Workflow Visualization Area */}
              <div className="relative mt-3 p-1 bg-black/20 rounded-xl border border-white/5">
                <div className="aspect-[3/2] w-full">
                  {activeFeatureCard === 'routing' && (
                    <div className="w-full h-full relative">
                      <Image
                        src="/Example_1.png"
                        alt="RouKey Model Routing Example"
                        width={1536}
                        height={1024}
                        className="w-full h-full object-contain rounded-lg"
                        quality={95}
                        priority
                      />
                    </div>
                  )}
                  {activeFeatureCard === 'api' && (
                    <div className="w-full h-full bg-gray-900 rounded-lg border border-gray-700 p-6 overflow-auto">
                      <div className="font-mono text-sm">
                        <div className="text-green-400 mb-4"># RouKey API Usage Example</div>
                        <div className="text-gray-300 mb-2">
                          <span className="text-blue-400">import</span> requests
                        </div>
                        <div className="text-gray-300 mb-4">
                          <span className="text-blue-400">import</span> json
                        </div>
                        <div className="text-gray-300 mb-2">
                          url = <span className="text-yellow-300">"https://api.roukey.online/v1/chat/completions"</span>
                        </div>
                        <div className="text-gray-300 mb-2">
                          headers = {"{"}
                        </div>
                        <div className="text-gray-300 mb-2 ml-4">
                          <span className="text-yellow-300">"X-API-Key"</span>: <span className="text-yellow-300">"your-roukey-api-key"</span>,
                        </div>
                        <div className="text-gray-300 mb-2 ml-4">
                          <span className="text-yellow-300">"Content-Type"</span>: <span className="text-yellow-300">"application/json"</span>
                        </div>
                        <div className="text-gray-300 mb-4">{"}"}</div>
                        <div className="text-gray-300 mb-2">
                          data = {"{"}
                        </div>
                        <div className="text-gray-300 mb-2 ml-4">
                          <span className="text-yellow-300">"model"</span>: <span className="text-yellow-300">"gpt-4"</span>,
                        </div>
                        <div className="text-gray-300 mb-2 ml-4">
                          <span className="text-yellow-300">"messages"</span>: [
                        </div>
                        <div className="text-gray-300 mb-2 ml-8">
                          {"{\"role\": \"user\", \"content\": \"Analyze this data\"}"}
                        </div>
                        <div className="text-gray-300 mb-2 ml-4">]</div>
                        <div className="text-gray-300 mb-4">{"}"}</div>
                        <div className="text-gray-300">
                          response = requests.post(url, headers=headers, json=data)
                        </div>
                      </div>
                    </div>
                  )}
                  {!['routing', 'api'].includes(activeFeatureCard) && (
                    <div className="w-full h-full bg-gradient-to-br from-white/5 to-white/10 rounded-lg border border-white/10 flex items-center justify-center backdrop-blur-sm">
                      <div className="text-gray-300 text-center">
                        <div className="w-12 h-12 bg-white/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                          <span className="text-xl">
                            {activeFeatureCard === 'orchestration' && '🔗'}
                            {activeFeatureCard === 'costs' && '💰'}
                            {activeFeatureCard === 'demo' && '▶️'}
                          </span>
                        </div>
                        <div className="text-xl font-semibold mb-2 text-white">
                          {activeFeatureCard === 'orchestration' && 'Multi-Role Orchestration'}
                          {activeFeatureCard === 'costs' && 'Cost Optimization Dashboard'}
                          {activeFeatureCard === 'demo' && 'Live Demo Video'}
                        </div>
                        <div className="text-sm text-gray-400">Coming soon</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>


    </>
  );
}
