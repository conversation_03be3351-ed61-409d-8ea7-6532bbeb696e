'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRightIcon, PlayIcon, SparklesIcon, BoltIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';
import InstantLink from '@/components/ui/InstantLink';

export default function HeroSection() {
  const [activeFeatureCard, setActiveFeatureCard] = useState('itops');

  return (
    <>
      {/* Hero Section - N8N Style Rectangular */}
      <section className="relative h-[95vh] overflow-hidden" style={{
        backgroundImage: 'url(/Hero_Section_Background_Image.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}>
        {/* Subtle Grid Overlay with Glow */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.008)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.008)_1px,transparent_1px)] bg-[size:50px_50px]"
             style={{
               boxShadow: 'inset 0 0 100px rgba(255,255,255,0.02)'
             }} />

        {/* Dark overlay for text readability */}
        <div className="absolute inset-0 bg-black/40"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center h-full py-16">
            {/* Left Column - Content */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4 }}
              className="text-left"
            >
              {/* RouKey Hero Title */}
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
                Universal AI <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">gateway</span>
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">
                  with your own keys
                </span>
              </h1>

              <p className="text-base md:text-lg text-gray-300 mb-8 leading-relaxed max-w-lg">
                Connect to 50+ AI providers using your own API keys. Smart routing,
                fallback protection, and cost optimization. One unified interface
                for OpenAI, Claude, Gemini, and more - all with complete control
                over your data and costs.
              </p>

              {/* N8N-Style CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <InstantLink
                  href="/pricing"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-orange-500/25"
                >
                  Get started for free
                </InstantLink>

                <InstantLink
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 border border-white/20 text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-200"
                >
                  Talk to sales
                </InstantLink>
              </motion.div>
            </motion.div>

            {/* Right Column - Visual Element */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              className="relative flex justify-center items-center mt-64"
            >
              {/* Lightning Bolt Style Visual - Enhanced with Shine & Gloss */}
              <div className="relative">
                {/* Main Lightning Bolt with Enhanced Gradient */}
                <div className="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-400 via-orange-400 via-orange-500 to-orange-600 opacity-95 drop-shadow-2xl">
                  ⚡
                </div>

                {/* Glossy Highlight Overlay - Brighter */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-200 via-orange-300 to-transparent opacity-40">
                  ⚡
                </div>

                {/* Outer Glow - Brighter */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-orange-400/50 blur-2xl">
                  ⚡
                </div>

                {/* Inner Glow - Brighter */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-yellow-400/45 blur-lg">
                  ⚡
                </div>

                {/* Shine Effect - Brighter Animation */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-transparent via-yellow-300 to-transparent opacity-30 animate-pulse">
                  ⚡
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Feature Cards Section - Enhanced Design */}
      <section className="relative py-20" style={{
        background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
      }}>
        {/* Grid overlay to match other sections */}
        <div className="absolute inset-0 opacity-20">
          <div
            style={{
              backgroundImage: `
                linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)
              `,
              backgroundSize: '50px 50px'
            }}
          />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              See RouKey in Action
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Explore how different teams leverage RouKey's intelligent routing for their specific workflows
            </p>
          </div>

          {/* Enhanced Feature Cards Container */}
          <div className="relative">
            {/* Ambient glow background */}
            <div className="absolute inset-0 bg-gradient-to-r from-orange-500/20 via-purple-500/10 to-orange-500/20 rounded-3xl blur-3xl scale-110"></div>

            {/* Main container with glass morphism effect */}
            <div className="relative bg-white/5 backdrop-blur-xl rounded-3xl border border-white/10 shadow-2xl overflow-hidden">
              {/* Subtle inner glow */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5 rounded-3xl"></div>

              {/* Feature Cards Grid */}
              <div className="relative p-8">
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
                {/* IT Ops Card */}
                <button
                  onClick={() => setActiveFeatureCard('itops')}
                  className={`group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'itops'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'itops' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
                        activeFeatureCard === 'itops'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        ⚡
                      </div>
                      <div className="text-sm font-semibold">IT Ops <span className="text-gray-400 font-normal">can</span></div>
                    </div>
                    <div className="text-xs text-gray-300">
                      On-board new employees
                    </div>
                  </div>
                </button>

                {/* Sec Ops Card */}
                <button
                  onClick={() => setActiveFeatureCard('secops')}
                  className={`group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'secops'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'secops' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
                        activeFeatureCard === 'secops'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        🔒
                      </div>
                      <div className="text-sm font-semibold">Sec Ops <span className="text-gray-400 font-normal">can</span></div>
                    </div>
                    <div className="text-xs text-gray-300">
                      Enrich security incident tickets
                    </div>
                  </div>
                </button>

                {/* Dev Ops Card */}
                <button
                  onClick={() => setActiveFeatureCard('devops')}
                  className={`group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'devops'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'devops' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
                        activeFeatureCard === 'devops'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        🔧
                      </div>
                      <div className="text-sm font-semibold">Dev Ops <span className="text-gray-400 font-normal">can</span></div>
                    </div>
                    <div className="text-xs text-gray-300">
                      Convert natural language into API calls
                    </div>
                  </div>
                </button>

                {/* Sales Card */}
                <button
                  onClick={() => setActiveFeatureCard('sales')}
                  className={`group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'sales'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'sales' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
                        activeFeatureCard === 'sales'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        📊
                      </div>
                      <div className="text-sm font-semibold">Sales <span className="text-gray-400 font-normal">can</span></div>
                    </div>
                    <div className="text-xs text-gray-300">
                      Generate customer insights from reviews
                    </div>
                  </div>
                </button>

                {/* You Card */}
                <button
                  onClick={() => setActiveFeatureCard('you')}
                  className={`group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden ${
                    activeFeatureCard === 'you'
                      ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105'
                      : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'
                  }`}
                >
                  {/* Background glow for active state */}
                  {activeFeatureCard === 'you' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl"></div>
                  )}

                  <div className="relative">
                    <div className="flex items-center mb-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
                        activeFeatureCard === 'you'
                          ? 'bg-orange-500/30 text-orange-300'
                          : 'bg-white/10 text-gray-400 group-hover:bg-white/20'
                      }`}>
                        ▶️
                      </div>
                      <div className="text-sm font-semibold">You <span className="text-gray-400 font-normal">can</span></div>
                    </div>
                    <div className="text-xs text-gray-300">
                      Watch this video to hear our pitch
                    </div>
                  </div>
                </button>
              </div>
            </div>

              {/* Workflow Visualization Area */}
              <div className="relative mt-8 p-8 bg-black/20 rounded-2xl border border-white/5">
                <div className="aspect-[16/10] w-full">
                  {activeFeatureCard === 'itops' && (
                    <div className="w-full h-full relative">
                      <Image
                        src="/Example_1.png"
                        alt="RouKey Workflow Example"
                        width={800}
                        height={500}
                        className="w-full h-full object-contain rounded-xl shadow-2xl"
                      />
                      {/* Enhanced overlay for better integration */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-xl pointer-events-none"></div>
                      {/* Glow effect around image */}
                      <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-transparent rounded-xl blur-xl"></div>
                    </div>
                  )}
                  {activeFeatureCard !== 'itops' && (
                    <div className="w-full h-full bg-gradient-to-br from-white/5 to-white/10 rounded-xl border border-white/10 flex items-center justify-center backdrop-blur-sm">
                      <div className="text-gray-300 text-center">
                        <div className="w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
                          <span className="text-2xl">🚀</span>
                        </div>
                        <div className="text-2xl font-semibold mb-4 text-white">Workflow Visualization</div>
                        <div className="text-lg text-gray-400">Coming Soon for {activeFeatureCard}</div>
                        <div className="text-sm text-gray-500 mt-2">Interactive demo in development</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>


    </>
  );
}
