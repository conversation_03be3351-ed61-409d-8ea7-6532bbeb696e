"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/HeroSection.tsx":
/*!************************************************!*\
  !*** ./src/components/landing/HeroSection.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction HeroSection() {\n    _s();\n    const [activeFeatureCard, setActiveFeatureCard] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('itops');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative h-[95vh] overflow-hidden\",\n                style: {\n                    backgroundImage: 'url(/Hero_Section_Background_Image.png)',\n                    backgroundSize: 'cover',\n                    backgroundPosition: 'center',\n                    backgroundRepeat: 'no-repeat'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.008)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.008)_1px,transparent_1px)] bg-[size:50px_50px]\",\n                        style: {\n                            boxShadow: 'inset 0 0 100px rgba(255,255,255,0.02)'\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-black/40\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center h-full py-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: -30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.4\n                                    },\n                                    className: \"text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight\",\n                                            children: [\n                                                \"Universal AI \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600\",\n                                                    children: \"gateway\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 30\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600\",\n                                                    children: \"with your own keys\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 44,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-base md:text-lg text-gray-300 mb-8 leading-relaxed max-w-lg\",\n                                            children: \"Connect to 50+ AI providers using your own API keys. Smart routing, fallback protection, and cost optimization. One unified interface for OpenAI, Claude, Gemini, and more - all with complete control over your data and costs.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 15\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.3,\n                                                delay: 0.2\n                                            },\n                                            className: \"flex flex-col sm:flex-row gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/pricing\",\n                                                    className: \"inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-orange-500/25\",\n                                                    children: \"Get started for free\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/contact\",\n                                                    className: \"inline-flex items-center px-6 py-3 border border-white/20 text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-200\",\n                                                    children: \"Talk to sales\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.4,\n                                        delay: 0.2\n                                    },\n                                    className: \"relative flex justify-center items-center mt-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-400 via-orange-400 via-orange-500 to-orange-600 opacity-95 drop-shadow-2xl\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-200 via-orange-300 to-transparent opacity-40\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 text-8xl md:text-9xl font-bold text-orange-400/50 blur-2xl\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 text-8xl md:text-9xl font-bold text-yellow-400/45 blur-lg\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-transparent via-yellow-300 to-transparent opacity-30 animate-pulse\",\n                                                children: \"⚡\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20\",\n                style: {\n                    background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundImage: \"\\n                linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n                linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n              \",\n                                backgroundSize: '50px 50px'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                                        children: \"See RouKey in Action\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                        children: \"Explore how different teams leverage RouKey's intelligent routing for their specific workflows\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-orange-500/20 via-purple-500/10 to-orange-500/20 rounded-3xl blur-3xl scale-110\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative bg-white/5 backdrop-blur-xl rounded-3xl border border-white/10 shadow-2xl overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5 rounded-3xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative p-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-5 gap-4 mb-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('itops'),\n                                                            className: \"group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'itops' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'itops' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 rounded-lg flex items-center justify-center mr-3 \".concat(activeFeatureCard === 'itops' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"⚡\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 175,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm font-semibold\",\n                                                                                    children: [\n                                                                                        \"IT Ops \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400 font-normal\",\n                                                                                            children: \"can\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                            lineNumber: 182,\n                                                                                            columnNumber: 69\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 182,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-300\",\n                                                                            children: \"On-board new employees\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('secops'),\n                                                            className: \"group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'secops' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'secops' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 201,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 rounded-lg flex items-center justify-center mr-3 \".concat(activeFeatureCard === 'secops' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"\\uD83D\\uDD12\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 206,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm font-semibold\",\n                                                                                    children: [\n                                                                                        \"Sec Ops \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400 font-normal\",\n                                                                                            children: \"can\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                            lineNumber: 213,\n                                                                                            columnNumber: 70\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 213,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 205,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-300\",\n                                                                            children: \"Enrich security incident tickets\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 215,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 204,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('devops'),\n                                                            className: \"group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'devops' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'devops' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 232,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 rounded-lg flex items-center justify-center mr-3 \".concat(activeFeatureCard === 'devops' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"\\uD83D\\uDD27\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 237,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm font-semibold\",\n                                                                                    children: [\n                                                                                        \"Dev Ops \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400 font-normal\",\n                                                                                            children: \"can\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                            lineNumber: 244,\n                                                                                            columnNumber: 70\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 244,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-300\",\n                                                                            children: \"Convert natural language into API calls\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 246,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('sales'),\n                                                            className: \"group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'sales' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'sales' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 rounded-lg flex items-center justify-center mr-3 \".concat(activeFeatureCard === 'sales' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"\\uD83D\\uDCCA\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 268,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm font-semibold\",\n                                                                                    children: [\n                                                                                        \"Sales \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400 font-normal\",\n                                                                                            children: \"can\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                            lineNumber: 275,\n                                                                                            columnNumber: 68\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 275,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-300\",\n                                                                            children: \"Generate customer insights from reviews\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 277,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setActiveFeatureCard('you'),\n                                                            className: \"group relative p-6 rounded-2xl border transition-all duration-300 text-left overflow-hidden \".concat(activeFeatureCard === 'you' ? 'bg-gradient-to-br from-orange-500/20 to-orange-600/10 border-orange-400/50 text-white shadow-xl shadow-orange-500/25 scale-105' : 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10 hover:border-white/20 hover:scale-102'),\n                                                            children: [\n                                                                activeFeatureCard === 'you' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/30 to-transparent rounded-2xl blur-xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center mb-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-8 h-8 rounded-lg flex items-center justify-center mr-3 \".concat(activeFeatureCard === 'you' ? 'bg-orange-500/30 text-orange-300' : 'bg-white/10 text-gray-400 group-hover:bg-white/20'),\n                                                                                    children: \"▶️\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 299,\n                                                                                    columnNumber: 23\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"text-sm font-semibold\",\n                                                                                    children: [\n                                                                                        \"You \",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-gray-400 font-normal\",\n                                                                                            children: \"can\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                            lineNumber: 306,\n                                                                                            columnNumber: 66\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                                    lineNumber: 306,\n                                                                                    columnNumber: 23\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 21\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-300\",\n                                                                            children: \"Watch this video to hear our pitch\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 308,\n                                                                            columnNumber: 21\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mt-8 p-8 bg-black/20 rounded-2xl border border-white/5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"aspect-[16/10] w-full\",\n                                                    children: [\n                                                        activeFeatureCard === 'itops' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                    src: \"/Example_1.png\",\n                                                                    alt: \"RouKey Workflow Example\",\n                                                                    width: 800,\n                                                                    height: 500,\n                                                                    className: \"w-full h-full object-contain rounded-xl shadow-2xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent rounded-xl pointer-events-none\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-br from-orange-500/10 to-transparent rounded-xl blur-xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        activeFeatureCard !== 'itops' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gradient-to-br from-white/5 to-white/10 rounded-xl border border-white/10 flex items-center justify-center backdrop-blur-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-gray-300 text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center mx-auto mb-6\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-2xl\",\n                                                                            children: \"\\uD83D\\uDE80\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-semibold mb-4 text-white\",\n                                                                        children: \"Workflow Visualization\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-lg text-gray-400\",\n                                                                        children: [\n                                                                            \"Coming Soon for \",\n                                                                            activeFeatureCard\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500 mt-2\",\n                                                                        children: \"Interactive demo in development\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\HeroSection.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(HeroSection, \"cig8L/G+gHsuYtoqp7K5Yp1yaME=\");\n_c = HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/HeroSection.tsx\n"));

/***/ })

});