"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx":
/*!**********************************************************!*\
  !*** ./src/components/landing/AIIntegrationsSection.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIIntegrationsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst aiProviders = [\n    // Row 1 - Core providers with verified working logos from LobeHub\n    {\n        name: 'OpenAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/openai.png',\n        alt: 'OpenAI GPT Models'\n    },\n    {\n        name: 'Anthropic',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/anthropic.png',\n        alt: 'Anthropic Claude'\n    },\n    {\n        name: 'Google',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/gemini-color.png',\n        alt: 'Google Gemini'\n    },\n    {\n        name: 'Meta',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/meta-color.png',\n        alt: 'Meta Llama Models'\n    },\n    {\n        name: 'Mistral AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/mistral-color.png',\n        alt: 'Mistral AI'\n    },\n    {\n        name: 'DeepSeek',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/deepseek-color.png',\n        alt: 'DeepSeek'\n    },\n    {\n        name: 'Microsoft',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/microsoft-color.png',\n        alt: 'Microsoft Azure'\n    },\n    {\n        name: 'Cohere',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/cohere-color.png',\n        alt: 'Cohere'\n    },\n    {\n        name: 'Perplexity',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/perplexity-color.png',\n        alt: 'Perplexity'\n    },\n    {\n        name: 'xAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/xai.png',\n        alt: 'xAI Grok'\n    },\n    {\n        name: 'NVIDIA',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/nvidia-color.png',\n        alt: 'NVIDIA'\n    },\n    {\n        name: 'Replicate',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/replicate.png',\n        alt: 'Replicate'\n    },\n    // Row 2 - Additional verified providers from LobeHub\n    {\n        name: 'AI21 Labs',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/ai21.png',\n        alt: 'AI21 Labs'\n    },\n    {\n        name: 'Amazon',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/bedrock-color.png',\n        alt: 'Amazon Bedrock'\n    },\n    {\n        name: 'Alibaba',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/alibaba-color.png',\n        alt: 'Alibaba Qwen Models'\n    },\n    {\n        name: 'Zhipu AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/chatglm-color.png',\n        alt: 'Zhipu AI GLM'\n    },\n    {\n        name: 'Yi AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/yi-color.png',\n        alt: 'Yi AI'\n    },\n    {\n        name: 'Hugging Face',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/huggingface-color.png',\n        alt: 'Hugging Face'\n    },\n    {\n        name: 'Moonshot',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/moonshot.png',\n        alt: 'Moonshot AI'\n    },\n    {\n        name: 'Baidu',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baidu-color.png',\n        alt: 'Baidu Wenxin'\n    },\n    {\n        name: 'ByteDance',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/doubao-color.png',\n        alt: 'ByteDance Doubao'\n    },\n    {\n        name: 'Minimax',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/minimax-color.png',\n        alt: 'Minimax'\n    },\n    {\n        name: 'Baichuan',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baichuan-color.png',\n        alt: 'Baichuan AI'\n    },\n    {\n        name: 'Together AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/together-color.png',\n        alt: 'Together AI'\n    }\n];\nfunction AIIntegrationsSection() {\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"fc54644e8a51ca7e\",\n                children: \"@-webkit-keyframes scroll-right{0%{-webkit-transform:translatex(0);transform:translatex(0)}100%{-webkit-transform:translatex(-50%);transform:translatex(-50%)}}@-moz-keyframes scroll-right{0%{-moz-transform:translatex(0);transform:translatex(0)}100%{-moz-transform:translatex(-50%);transform:translatex(-50%)}}@-o-keyframes scroll-right{0%{-o-transform:translatex(0);transform:translatex(0)}100%{-o-transform:translatex(-50%);transform:translatex(-50%)}}@keyframes scroll-right{0%{-webkit-transform:translatex(0);-moz-transform:translatex(0);-o-transform:translatex(0);transform:translatex(0)}100%{-webkit-transform:translatex(-50%);-moz-transform:translatex(-50%);-o-transform:translatex(-50%);transform:translatex(-50%)}}@-webkit-keyframes scroll-left{0%{-webkit-transform:translatex(-50%);transform:translatex(-50%)}100%{-webkit-transform:translatex(0);transform:translatex(0)}}@-moz-keyframes scroll-left{0%{-moz-transform:translatex(-50%);transform:translatex(-50%)}100%{-moz-transform:translatex(0);transform:translatex(0)}}@-o-keyframes scroll-left{0%{-o-transform:translatex(-50%);transform:translatex(-50%)}100%{-o-transform:translatex(0);transform:translatex(0)}}@keyframes scroll-left{0%{-webkit-transform:translatex(-50%);-moz-transform:translatex(-50%);-o-transform:translatex(-50%);transform:translatex(-50%)}100%{-webkit-transform:translatex(0);-moz-transform:translatex(0);-o-transform:translatex(0);transform:translatex(0)}}.scroll-right.jsx-fc54644e8a51ca7e{-webkit-animation:scroll-right 30s linear infinite;-moz-animation:scroll-right 30s linear infinite;-o-animation:scroll-right 30s linear infinite;animation:scroll-right 30s linear infinite}.scroll-left.jsx-fc54644e8a51ca7e{-webkit-animation:scroll-left 30s linear infinite;-moz-animation:scroll-left 30s linear infinite;-o-animation:scroll-left 30s linear infinite;animation:scroll-left 30s linear infinite}.scroll-container.jsx-fc54644e8a51ca7e{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;width:200%;will-change:transform}.scroll-track.jsx-fc54644e8a51ca7e{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;gap:1rem;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;width:50%}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                ref: ref,\n                className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"relative py-20 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                        },\n                        className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundImage: \"\\n              linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n            \",\n                                backgroundSize: '50px 50px'\n                            },\n                            className: \"jsx-fc54644e8a51ca7e\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                                        children: [\n                                            \"Connect to any AI model with\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                children: \"300+ integrations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                        children: \"RouKey provides unified access to every major AI provider through a single API. No vendor lock-in, just seamless AI integration.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                className: \"relative mb-8 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"scroll-container scroll-right\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"scroll-track\",\n                                            children: [\n                                                ...aiProviders.slice(0, 12),\n                                                ...aiProviders.slice(0, 12)\n                                            ].map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"group relative flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: provider.logo,\n                                                                    alt: provider.alt,\n                                                                    width: 32,\n                                                                    height: 32,\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                    lineNumber: 145,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                lineNumber: 144,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                            children: provider.alt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, \"right-\".concat(index), true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"scroll-track\",\n                                            children: [\n                                                ...aiProviders.slice(0, 12),\n                                                ...aiProviders.slice(0, 12)\n                                            ].map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"group relative flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: provider.logo,\n                                                                    alt: provider.alt,\n                                                                    width: 32,\n                                                                    height: 32,\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                    lineNumber: 171,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                lineNumber: 170,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                            children: provider.alt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, \"right-dup-\".concat(index), true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                className: \"relative mb-12 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"scroll-container scroll-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"scroll-track\",\n                                            children: [\n                                                ...aiProviders.slice(12, 24),\n                                                ...aiProviders.slice(12, 24)\n                                            ].map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"group relative flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: provider.logo,\n                                                                    alt: provider.alt,\n                                                                    width: 32,\n                                                                    height: 32,\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                    lineNumber: 209,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                            children: provider.alt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, \"left-\".concat(index), true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"scroll-track\",\n                                            children: [\n                                                ...aiProviders.slice(12, 24),\n                                                ...aiProviders.slice(12, 24)\n                                            ].map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"group relative flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: provider.logo,\n                                                                    alt: provider.alt,\n                                                                    width: 32,\n                                                                    height: 32,\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                    lineNumber: 235,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                            children: provider.alt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, \"left-dup-\".concat(index), true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.6\n                                },\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"text-gray-400 mb-6\",\n                                        children: \"And 270+ more providers available through RouKey's unified API\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"px-8 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105\",\n                                                children: \"View All Integrations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"jsx-fc54644e8a51ca7e\" + \" \" + \"px-8 py-3 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300\",\n                                                children: \"Start Free Trial\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AIIntegrationsSection, \"QMBuJFIdzLIeqBcFwhMf246mjOM=\");\n_c = AIIntegrationsSection;\nvar _c;\n$RefreshReg$(_c, \"AIIntegrationsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx\n"));

/***/ })

});