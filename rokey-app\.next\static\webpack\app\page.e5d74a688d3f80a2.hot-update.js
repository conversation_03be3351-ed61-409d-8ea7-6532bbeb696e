"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx":
/*!**********************************************************!*\
  !*** ./src/components/landing/AIIntegrationsSection.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIIntegrationsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst aiProviders = [\n    // Row 1 - Core providers with verified working logos from LobeHub\n    {\n        name: 'OpenAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/openai.png',\n        alt: 'OpenAI GPT Models'\n    },\n    {\n        name: 'Anthropic',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/anthropic.png',\n        alt: 'Anthropic Claude'\n    },\n    {\n        name: 'Google',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/gemini-color.png',\n        alt: 'Google Gemini'\n    },\n    {\n        name: 'Meta',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/meta-color.png',\n        alt: 'Meta Llama Models'\n    },\n    {\n        name: 'Mistral AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/mistral-color.png',\n        alt: 'Mistral AI'\n    },\n    {\n        name: 'DeepSeek',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/deepseek-color.png',\n        alt: 'DeepSeek'\n    },\n    {\n        name: 'Microsoft',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/microsoft-color.png',\n        alt: 'Microsoft Azure'\n    },\n    {\n        name: 'Cohere',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/cohere-color.png',\n        alt: 'Cohere'\n    },\n    {\n        name: 'Perplexity',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/perplexity-color.png',\n        alt: 'Perplexity'\n    },\n    {\n        name: 'xAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/xai.png',\n        alt: 'xAI Grok'\n    },\n    {\n        name: 'NVIDIA',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/nvidia-color.png',\n        alt: 'NVIDIA'\n    },\n    {\n        name: 'Replicate',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/replicate.png',\n        alt: 'Replicate'\n    },\n    // Row 2 - Additional verified providers from LobeHub\n    {\n        name: 'AI21 Labs',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/ai21.png',\n        alt: 'AI21 Labs'\n    },\n    {\n        name: 'Amazon',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/bedrock-color.png',\n        alt: 'Amazon Bedrock'\n    },\n    {\n        name: 'Alibaba',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/alibaba-color.png',\n        alt: 'Alibaba Qwen Models'\n    },\n    {\n        name: 'Zhipu AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/chatglm-color.png',\n        alt: 'Zhipu AI GLM'\n    },\n    {\n        name: 'Yi AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/yi-color.png',\n        alt: 'Yi AI'\n    },\n    {\n        name: 'Hugging Face',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/huggingface-color.png',\n        alt: 'Hugging Face'\n    },\n    {\n        name: 'Moonshot',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/moonshot.png',\n        alt: 'Moonshot AI'\n    },\n    {\n        name: 'Baidu',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baidu-color.png',\n        alt: 'Baidu Wenxin'\n    },\n    {\n        name: 'ByteDance',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/doubao-color.png',\n        alt: 'ByteDance Doubao'\n    },\n    {\n        name: 'Minimax',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/minimax-color.png',\n        alt: 'Minimax'\n    },\n    {\n        name: 'Baichuan',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baichuan-color.png',\n        alt: 'Baichuan AI'\n    },\n    {\n        name: 'Together AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/together-color.png',\n        alt: 'Together AI'\n    }\n];\n// Custom hook for scroll-based animations\nfunction useScrollAnimation() {\n    _s();\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useScrollAnimation.useEffect\": ()=>{\n            const handleScroll = {\n                \"useScrollAnimation.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"useScrollAnimation.useEffect.handleScroll\"];\n            const observer = new IntersectionObserver({\n                \"useScrollAnimation.useEffect\": (param)=>{\n                    let [entry] = param;\n                    setIsVisible(entry.isIntersecting);\n                }\n            }[\"useScrollAnimation.useEffect\"], {\n                threshold: 0.1\n            });\n            if (ref.current) {\n                observer.observe(ref.current);\n            }\n            window.addEventListener('scroll', handleScroll, {\n                passive: true\n            });\n            return ({\n                \"useScrollAnimation.useEffect\": ()=>{\n                    window.removeEventListener('scroll', handleScroll);\n                    if (ref.current) {\n                        observer.unobserve(ref.current);\n                    }\n                }\n            })[\"useScrollAnimation.useEffect\"];\n        }\n    }[\"useScrollAnimation.useEffect\"], []);\n    return {\n        scrollY,\n        isVisible,\n        ref\n    };\n}\n_s(useScrollAnimation, \"nzxjuC2Kq5Ot1yzB0Kyf6EQhEic=\");\nfunction AIIntegrationsSection() {\n    _s1();\n    const { scrollY, isVisible, ref } = useScrollAnimation();\n    // Calculate transform values based on scroll position\n    const getTransform = function(index) {\n        let isSecondRow = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!isVisible) return {};\n        const baseOffset = scrollY * 0.1;\n        const rowMultiplier = isSecondRow ? -1 : 1;\n        const itemOffset = index % 6 * 2; // Stagger effect\n        const translateY = baseOffset * rowMultiplier + itemOffset;\n        const rotateZ = Math.sin(scrollY * 0.01 + index) * 2; // Subtle rotation\n        const scale = 1 + Math.sin(scrollY * 0.005 + index) * 0.02; // Subtle scale\n        return {\n            transform: \"translateY(\".concat(translateY, \"px) rotateZ(\").concat(rotateZ, \"deg) scale(\").concat(scale, \")\"),\n            transition: 'transform 0.1s ease-out'\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"relative py-20 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                style: {\n                    background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundImage: \"\\n              linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n            \",\n                        backgroundSize: '50px 50px'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                                children: [\n                                    \"Connect to any AI model with\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: \"300+ integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"RouKey provides unified access to every major AI provider through a single API. No vendor lock-in, just seamless AI integration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-8\",\n                        children: aiProviders.slice(0, 12).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                style: getTransform(index, false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-12\",\n                        children: aiProviders.slice(12, 24).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"And 270+ more providers available through RouKey's unified API\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105\",\n                                        children: \"View All Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300\",\n                                        children: \"Start Free Trial\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s1(AIIntegrationsSection, \"L7HRw6zfXW83el553UQNuEdZsTc=\", false, function() {\n    return [\n        useScrollAnimation\n    ];\n});\n_c = AIIntegrationsSection;\nvar _c;\n$RefreshReg$(_c, \"AIIntegrationsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx\n"));

/***/ })

});