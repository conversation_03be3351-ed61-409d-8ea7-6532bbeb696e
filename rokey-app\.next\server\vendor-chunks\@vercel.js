"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel";
exports.ids = ["vendor-chunks/@vercel"];
exports.modules = {

/***/ "(rsc)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const SpeedInsights = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SpeedInsights() from the server but SpeedInsights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\RoKey App\\rokey-app\\node_modules\\@vercel\\speed-insights\\dist\\next\\index.mjs",
"SpeedInsights",
);

/***/ }),

/***/ "(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SpeedInsights auto */ // src/nextjs/index.tsx\n\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/speed-insights\";\nvar version = \"1.2.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.si) return;\n    window.si = function a(...params) {\n        (window.siq = window.siq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction isDevelopment() {\n    return detectEnvironment() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[${key}]`);\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[...${key}]`);\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js\";\n    }\n    if (props.dsn) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/speed-insights/script.js`;\n    }\n    return \"/_vercel/speed-insights/script.js\";\n}\n// src/generic.ts\nfunction injectSpeedInsights(props = {}) {\n    var _a;\n    if (!isBrowser() || props.route === null) return null;\n    initQueue();\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n    if (props.beforeSend) {\n        (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.sampleRate) {\n        script.dataset.sampleRate = props.sampleRate.toString();\n    }\n    if (props.route) {\n        script.dataset.route = props.route;\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    script.onerror = ()=>{\n        console.log(`[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`);\n    };\n    document.head.appendChild(script);\n    return {\n        setRoute: (route)=>{\n            script.dataset.route = route ?? void 0;\n        }\n    };\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction SpeedInsights(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SpeedInsights.useEffect\": ()=>{\n            var _a;\n            if (props.beforeSend) {\n                (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n            }\n        }\n    }[\"SpeedInsights.useEffect\"], [\n        props.beforeSend\n    ]);\n    const setScriptRoute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SpeedInsights.useEffect\": ()=>{\n            if (!setScriptRoute.current) {\n                const script = injectSpeedInsights({\n                    framework: props.framework ?? \"react\",\n                    basePath: props.basePath ?? getBasePath(),\n                    ...props\n                });\n                if (script) {\n                    setScriptRoute.current = script.setRoute;\n                }\n            } else if (props.route) {\n                setScriptRoute.current(props.route);\n            }\n        }\n    }[\"SpeedInsights.useEffect\"], [\n        props.route\n    ]);\n    return null;\n}\n// src/nextjs/utils.ts\n\nvar useRoute = ()=>{\n    const params = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)() || new URLSearchParams();\n    const path = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    if (!params) {\n        return null;\n    }\n    const finalParams = Object.keys(params).length ? params : Object.fromEntries(searchParams.entries());\n    return computeRoute(path, finalParams);\n};\nfunction getBasePath2() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/nextjs/index.tsx\nfunction SpeedInsightsComponent(props) {\n    const route = useRoute();\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsights, {\n        route,\n        ...props,\n        framework: \"next\",\n        basePath: getBasePath2()\n    });\n}\nfunction SpeedInsights2(props) {\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n        fallback: null\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsightsComponent, {\n        ...props\n    }));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs\n");

/***/ })

};
;