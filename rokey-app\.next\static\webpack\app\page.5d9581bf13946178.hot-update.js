"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx":
/*!**********************************************************!*\
  !*** ./src/components/landing/AIIntegrationsSection.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIIntegrationsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst aiProviders = [\n    // Row 1 - Core providers with verified working logos from LobeHub\n    {\n        name: 'OpenAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/openai.png',\n        alt: 'OpenAI GPT Models'\n    },\n    {\n        name: 'Anthropic',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/anthropic.png',\n        alt: 'Anthropic Claude'\n    },\n    {\n        name: 'Google',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/gemini-color.png',\n        alt: 'Google Gemini'\n    },\n    {\n        name: 'Meta',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/meta-color.png',\n        alt: 'Meta Llama Models'\n    },\n    {\n        name: 'Mistral AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/mistral-color.png',\n        alt: 'Mistral AI'\n    },\n    {\n        name: 'DeepSeek',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/deepseek-color.png',\n        alt: 'DeepSeek'\n    },\n    {\n        name: 'Microsoft',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/microsoft-color.png',\n        alt: 'Microsoft Azure'\n    },\n    {\n        name: 'Cohere',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/cohere-color.png',\n        alt: 'Cohere'\n    },\n    {\n        name: 'Perplexity',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/perplexity-color.png',\n        alt: 'Perplexity'\n    },\n    {\n        name: 'xAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/xai.png',\n        alt: 'xAI Grok'\n    },\n    {\n        name: 'NVIDIA',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/nvidia-color.png',\n        alt: 'NVIDIA'\n    },\n    {\n        name: 'Replicate',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/replicate.png',\n        alt: 'Replicate'\n    },\n    // Row 2 - Additional verified providers from LobeHub\n    {\n        name: 'AI21 Labs',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/ai21.png',\n        alt: 'AI21 Labs'\n    },\n    {\n        name: 'Amazon',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/bedrock-color.png',\n        alt: 'Amazon Bedrock'\n    },\n    {\n        name: 'Alibaba',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/alibaba-color.png',\n        alt: 'Alibaba Qwen Models'\n    },\n    {\n        name: 'Zhipu AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/chatglm-color.png',\n        alt: 'Zhipu AI GLM'\n    },\n    {\n        name: 'Yi AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/yi-color.png',\n        alt: 'Yi AI'\n    },\n    {\n        name: 'Hugging Face',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/huggingface-color.png',\n        alt: 'Hugging Face'\n    },\n    {\n        name: 'Moonshot',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/moonshot.png',\n        alt: 'Moonshot AI'\n    },\n    {\n        name: 'Baidu',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baidu-color.png',\n        alt: 'Baidu Wenxin'\n    },\n    {\n        name: 'ByteDance',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/doubao-color.png',\n        alt: 'ByteDance Doubao'\n    },\n    {\n        name: 'Minimax',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/minimax-color.png',\n        alt: 'Minimax'\n    },\n    {\n        name: 'Baichuan',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baichuan-color.png',\n        alt: 'Baichuan AI'\n    },\n    {\n        name: 'Together AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/together-color.png',\n        alt: 'Together AI'\n    }\n];\nfunction AIIntegrationsSection() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative py-20 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                style: {\n                    background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundImage: \"\\n              linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n            \",\n                        backgroundSize: '50px 50px'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                                children: [\n                                    \"Connect to any AI model with\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: \"300+ integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"RouKey provides unified access to every major AI provider through a single API. No vendor lock-in, just seamless AI integration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-8\",\n                        children: aiProviders.slice(0, 12).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-12\",\n                        children: aiProviders.slice(12, 24).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"And 270+ more providers available through RouKey's unified API\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105\",\n                                        children: \"View All Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300\",\n                                        children: \"Start Free Trial\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = AIIntegrationsSection;\nvar _c;\n$RefreshReg$(_c, \"AIIntegrationsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx\n"));

/***/ })

});