"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx":
/*!**********************************************************!*\
  !*** ./src/components/landing/AIIntegrationsSection.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIIntegrationsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst aiProviders = [\n    // Row 1 - Core providers with verified working logos from LobeHub\n    {\n        name: 'OpenAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/openai.png',\n        alt: 'OpenAI GPT Models'\n    },\n    {\n        name: 'Anthropic',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/anthropic.png',\n        alt: 'Anthropic Claude'\n    },\n    {\n        name: 'Google',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/gemini-color.png',\n        alt: 'Google Gemini'\n    },\n    {\n        name: 'Meta',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/meta-color.png',\n        alt: 'Meta Llama Models'\n    },\n    {\n        name: 'Mistral AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/mistral-color.png',\n        alt: 'Mistral AI'\n    },\n    {\n        name: 'DeepSeek',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/deepseek-color.png',\n        alt: 'DeepSeek'\n    },\n    {\n        name: 'Microsoft',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/microsoft-color.png',\n        alt: 'Microsoft Azure'\n    },\n    {\n        name: 'Cohere',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/cohere-color.png',\n        alt: 'Cohere'\n    },\n    {\n        name: 'Perplexity',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/perplexity-color.png',\n        alt: 'Perplexity'\n    },\n    {\n        name: 'xAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/xai.png',\n        alt: 'xAI Grok'\n    },\n    {\n        name: 'NVIDIA',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/nvidia-color.png',\n        alt: 'NVIDIA'\n    },\n    {\n        name: 'Replicate',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/replicate.png',\n        alt: 'Replicate'\n    },\n    // Row 2 - Additional verified providers from LobeHub\n    {\n        name: 'AI21 Labs',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/ai21.png',\n        alt: 'AI21 Labs'\n    },\n    {\n        name: 'Amazon',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/bedrock-color.png',\n        alt: 'Amazon Bedrock'\n    },\n    {\n        name: 'Alibaba',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/alibaba-color.png',\n        alt: 'Alibaba Qwen Models'\n    },\n    {\n        name: 'Zhipu AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/chatglm-color.png',\n        alt: 'Zhipu AI GLM'\n    },\n    {\n        name: 'Yi AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/yi-color.png',\n        alt: 'Yi AI'\n    },\n    {\n        name: 'Hugging Face',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/huggingface-color.png',\n        alt: 'Hugging Face'\n    },\n    {\n        name: 'Moonshot',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/moonshot.png',\n        alt: 'Moonshot AI'\n    },\n    {\n        name: 'Baidu',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baidu-color.png',\n        alt: 'Baidu Wenxin'\n    },\n    {\n        name: 'ByteDance',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/doubao-color.png',\n        alt: 'ByteDance Doubao'\n    },\n    {\n        name: 'Minimax',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/minimax-color.png',\n        alt: 'Minimax'\n    },\n    {\n        name: 'Baichuan',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baichuan-color.png',\n        alt: 'Baichuan AI'\n    },\n    {\n        name: 'Together AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/together-color.png',\n        alt: 'Together AI'\n    }\n];\nfunction AIIntegrationsSection() {\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"e12e79147a808821\",\n                children: \"@-webkit-keyframes scroll-right{0%{-webkit-transform:translatex(0);transform:translatex(0)}100%{-webkit-transform:translatex(-50%);transform:translatex(-50%)}}@-moz-keyframes scroll-right{0%{-moz-transform:translatex(0);transform:translatex(0)}100%{-moz-transform:translatex(-50%);transform:translatex(-50%)}}@-o-keyframes scroll-right{0%{-o-transform:translatex(0);transform:translatex(0)}100%{-o-transform:translatex(-50%);transform:translatex(-50%)}}@keyframes scroll-right{0%{-webkit-transform:translatex(0);-moz-transform:translatex(0);-o-transform:translatex(0);transform:translatex(0)}100%{-webkit-transform:translatex(-50%);-moz-transform:translatex(-50%);-o-transform:translatex(-50%);transform:translatex(-50%)}}@-webkit-keyframes scroll-left{0%{-webkit-transform:translatex(-50%);transform:translatex(-50%)}100%{-webkit-transform:translatex(0);transform:translatex(0)}}@-moz-keyframes scroll-left{0%{-moz-transform:translatex(-50%);transform:translatex(-50%)}100%{-moz-transform:translatex(0);transform:translatex(0)}}@-o-keyframes scroll-left{0%{-o-transform:translatex(-50%);transform:translatex(-50%)}100%{-o-transform:translatex(0);transform:translatex(0)}}@keyframes scroll-left{0%{-webkit-transform:translatex(-50%);-moz-transform:translatex(-50%);-o-transform:translatex(-50%);transform:translatex(-50%)}100%{-webkit-transform:translatex(0);-moz-transform:translatex(0);-o-transform:translatex(0);transform:translatex(0)}}.scroll-right.jsx-e12e79147a808821{-webkit-animation:scroll-right 20s linear infinite;-moz-animation:scroll-right 20s linear infinite;-o-animation:scroll-right 20s linear infinite;animation:scroll-right 20s linear infinite}.scroll-left.jsx-e12e79147a808821{-webkit-animation:scroll-left 20s linear infinite;-moz-animation:scroll-left 20s linear infinite;-o-animation:scroll-left 20s linear infinite;animation:scroll-left 20s linear infinite}.scroll-container.jsx-e12e79147a808821{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;width:200%}.scroll-track.jsx-e12e79147a808821{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;width:50%;gap:1rem;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                ref: ref,\n                className: \"jsx-e12e79147a808821\" + \" \" + \"relative py-20 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                        },\n                        className: \"jsx-e12e79147a808821\" + \" \" + \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-e12e79147a808821\" + \" \" + \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundImage: \"\\n              linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n            \",\n                                backgroundSize: '50px 50px'\n                            },\n                            className: \"jsx-e12e79147a808821\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-e12e79147a808821\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.6\n                                },\n                                className: \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"jsx-e12e79147a808821\" + \" \" + \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                                        children: [\n                                            \"Connect to any AI model with\",\n                                            ' ',\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-e12e79147a808821\" + \" \" + \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                                children: \"300+ integrations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-e12e79147a808821\" + \" \" + \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                        children: \"RouKey provides unified access to every major AI provider through a single API. No vendor lock-in, just seamless AI integration.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                className: \"relative mb-8 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-e12e79147a808821\" + \" \" + \"scroll-container\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-e12e79147a808821\" + \" \" + \"scroll-track scroll-right\",\n                                            children: aiProviders.slice(0, 12).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-e12e79147a808821\" + \" \" + \"group relative flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-e12e79147a808821\" + \" \" + \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-e12e79147a808821\" + \" \" + \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: provider.logo,\n                                                                    alt: provider.alt,\n                                                                    width: 32,\n                                                                    height: 32,\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                    lineNumber: 144,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-e12e79147a808821\" + \" \" + \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                            children: provider.alt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, \"first-\".concat(provider.name), true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-e12e79147a808821\" + \" \" + \"scroll-track scroll-right\",\n                                            children: aiProviders.slice(0, 12).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-e12e79147a808821\" + \" \" + \"group relative flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-e12e79147a808821\" + \" \" + \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-e12e79147a808821\" + \" \" + \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: provider.logo,\n                                                                    alt: provider.alt,\n                                                                    width: 32,\n                                                                    height: 32,\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                lineNumber: 169,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-e12e79147a808821\" + \" \" + \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                            children: provider.alt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, \"second-\".concat(provider.name), true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.4\n                                },\n                                className: \"relative mb-12 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-e12e79147a808821\" + \" \" + \"scroll-container\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-e12e79147a808821\" + \" \" + \"scroll-track scroll-left\",\n                                            children: aiProviders.slice(12, 24).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-e12e79147a808821\" + \" \" + \"group relative flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-e12e79147a808821\" + \" \" + \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-e12e79147a808821\" + \" \" + \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: provider.logo,\n                                                                    alt: provider.alt,\n                                                                    width: 32,\n                                                                    height: 32,\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-e12e79147a808821\" + \" \" + \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                            children: provider.alt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 219,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, \"first-\".concat(provider.name), true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-e12e79147a808821\" + \" \" + \"scroll-track scroll-left\",\n                                            children: aiProviders.slice(12, 24).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-e12e79147a808821\" + \" \" + \"group relative flex-shrink-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-e12e79147a808821\" + \" \" + \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-e12e79147a808821\" + \" \" + \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                                    src: provider.logo,\n                                                                    alt: provider.alt,\n                                                                    width: 32,\n                                                                    height: 32,\n                                                                    className: \"w-full h-full object-contain\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-e12e79147a808821\" + \" \" + \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10\",\n                                                            children: provider.alt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, \"second-\".concat(provider.name), true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.6\n                                },\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-e12e79147a808821\" + \" \" + \"text-gray-400 mb-6\",\n                                        children: \"And 270+ more providers available through RouKey's unified API\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-e12e79147a808821\" + \" \" + \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"jsx-e12e79147a808821\" + \" \" + \"px-8 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105\",\n                                                children: \"View All Integrations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"jsx-e12e79147a808821\" + \" \" + \"px-8 py-3 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300\",\n                                                children: \"Start Free Trial\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(AIIntegrationsSection, \"QMBuJFIdzLIeqBcFwhMf246mjOM=\");\n_c = AIIntegrationsSection;\nvar _c;\n$RefreshReg$(_c, \"AIIntegrationsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx\n"));

/***/ })

});