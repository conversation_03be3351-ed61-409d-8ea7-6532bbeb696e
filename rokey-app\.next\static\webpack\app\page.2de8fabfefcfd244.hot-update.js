"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx":
/*!*********************************************************!*\
  !*** ./src/components/landing/RoutingVisualization.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingVisualization)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CodeBracketIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BoltIcon,ChartBarIcon,CheckIcon,CodeBracketIcon,CpuChipIcon,PencilIcon,ShieldCheckIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst routingExamples = [\n    {\n        id: 1,\n        prompt: \"Solve this complex math problem: 2x + 5 = 15\",\n        role: \"logic_reasoning\",\n        roleName: \"Logic & Reasoning\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    },\n    {\n        id: 2,\n        prompt: \"Write a blog post about AI trends\",\n        role: \"writing\",\n        roleName: \"Writing & Content Creation\",\n        model: \"GPT-4o\",\n        provider: \"OpenAI\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    },\n    {\n        id: 3,\n        prompt: \"Build a React component with TypeScript\",\n        role: \"coding_frontend\",\n        roleName: \"Frontend Development\",\n        model: \"Claude 4 Opus\",\n        provider: \"Anthropic\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    },\n    {\n        id: 4,\n        prompt: \"Summarize this research paper\",\n        role: \"research_synthesis\",\n        roleName: \"Research & Analysis\",\n        model: \"DeepSeek R1 0528\",\n        provider: \"DeepSeek\",\n        icon: _barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"text-[#ff6b35]\",\n        bgColor: \"bg-[#ff6b35]/10\",\n        borderColor: \"border-[#ff6b35]/20\",\n        glowColor: \"shadow-[#ff6b35]/50\"\n    }\n];\nfunction RoutingVisualization() {\n    _s();\n    const [activeExample, setActiveExample] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RoutingVisualization.useEffect\": ()=>{\n            const interval = setInterval({\n                \"RoutingVisualization.useEffect.interval\": ()=>{\n                    setIsAnimating(true);\n                    setTimeout({\n                        \"RoutingVisualization.useEffect.interval\": ()=>{\n                            setActiveExample({\n                                \"RoutingVisualization.useEffect.interval\": (prev)=>(prev + 1) % routingExamples.length\n                            }[\"RoutingVisualization.useEffect.interval\"]);\n                            setIsAnimating(false);\n                        }\n                    }[\"RoutingVisualization.useEffect.interval\"], 200);\n                }\n            }[\"RoutingVisualization.useEffect.interval\"], 3000); // Faster transition for better flow\n            return ({\n                \"RoutingVisualization.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutingVisualization.useEffect\"];\n        }\n    }[\"RoutingVisualization.useEffect\"], []);\n    const currentExample = routingExamples[activeExample];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"jsx-34e24bce47f966\" + \" \" + \"relative py-20 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                },\n                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2edd7b34c21e4641\",\n                children: \"@-webkit-keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-moz-keyframes flowCurrent{0%{-moz-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-moz-transform:translatex(200%);transform:translatex(200%);opacity:0}}@-o-keyframes flowCurrent{0%{-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}@keyframes flowCurrent{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%);opacity:0}20%{opacity:1}80%{opacity:1}100%{-webkit-transform:translatex(200%);-moz-transform:translatex(200%);-o-transform:translatex(200%);transform:translatex(200%);opacity:0}}.current-flow.jsx-34e24bce47f966{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite}.current-flow-delayed.jsx-34e24bce47f966{-webkit-animation:flowCurrent 3s ease-in-out infinite;-moz-animation:flowCurrent 3s ease-in-out infinite;-o-animation:flowCurrent 3s ease-in-out infinite;animation:flowCurrent 3s ease-in-out infinite;-webkit-animation-delay:1.5s;-moz-animation-delay:1.5s;-o-animation-delay:1.5s;animation-delay:1.5s}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                backgroundImage: \"\\n                linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n                linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n              \",\n                                backgroundSize: '50px 50px'\n                            },\n                            className: \"jsx-34e24bce47f966\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundImage: \"\\n              linear-gradient(rgba(255, 107, 53, 0.25) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 107, 53, 0.25) 1px, transparent 1px),\\n              linear-gradient(rgba(0, 0, 0, 0.15) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(0, 0, 0, 0.15) 1px, transparent 1px),\\n              radial-gradient(circle at 25% 25%, rgba(255, 107, 53, 0.4) 2px, transparent 2px),\\n              radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.3) 1px, transparent 1px)\\n            \",\n                            backgroundSize: '80px 80px, 80px 80px, 40px 40px, 40px 40px, 160px 160px, 120px 120px',\n                            backgroundPosition: '0 0, 0 0, 20px 20px, 20px 20px, 0 0, 60px 60px',\n                            mask: \"\\n              radial-gradient(ellipse 90% 90% at center, black 20%, transparent 85%),\\n              linear-gradient(to right, transparent 5%, black 10%, black 90%, transparent 95%),\\n              linear-gradient(to bottom, transparent 5%, black 10%, black 90%, transparent 95%)\\n            \",\n                            maskComposite: 'intersect',\n                            WebkitMask: \"\\n              radial-gradient(ellipse 90% 90% at center, black 20%, transparent 85%),\\n              linear-gradient(to right, transparent 5%, black 10%, black 90%, transparent 95%),\\n              linear-gradient(to bottom, transparent 5%, black 10%, black 90%, transparent 95%)\\n            \",\n                            WebkitMaskComposite: 'source-in'\n                        },\n                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-1/4 left-1/4 w-96 h-96 bg-[#ff6b35]/8 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-[#f7931e]/8 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-1/2 right-1/3 w-64 h-64 bg-[#ff6b35]/6 rounded-full blur-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 pointer-events-none overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    animation: 'circuit-pulse 4s ease-in-out infinite'\n                                },\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#ff6b35]/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    animation: 'circuit-pulse 4s ease-in-out infinite',\n                                    animationDelay: '2s'\n                                },\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-[#ff6b35]/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    animation: 'circuit-pulse 4s ease-in-out infinite',\n                                    animationDelay: '1s'\n                                },\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute left-1/4 top-0 w-px h-full bg-gradient-to-b from-transparent via-[#ff6b35]/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    animation: 'circuit-pulse 4s ease-in-out infinite',\n                                    animationDelay: '3s'\n                                },\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute right-1/4 top-0 w-px h-full bg-gradient-to-b from-transparent via-[#ff6b35]/30 to-transparent\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"82dcaf3221b60e98\",\n                children: \"@-webkit-keyframes circuit-pulse{0%,100%{opacity:.3}50%{opacity:.8}}@-moz-keyframes circuit-pulse{0%,100%{opacity:.3}50%{opacity:.8}}@-o-keyframes circuit-pulse{0%,100%{opacity:.3}50%{opacity:.8}}@keyframes circuit-pulse{0%,100%{opacity:.3}50%{opacity:.8}}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-34e24bce47f966\" + \" \" + \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h2, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 15\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.3\n                                },\n                                className: \"text-4xl sm:text-5xl font-bold text-white mb-6\",\n                                children: \"Introducing the AI Gateway Pattern\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 15\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.3,\n                                    delay: 0.05\n                                },\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"Watch how RouKey intelligently routes your requests through our unified gateway to the perfect AI model\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-34e24bce47f966\" + \" \" + \"relative max-w-7xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/5 via-transparent to-green-500/5 rounded-3xl blur-3xl\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"relative grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-16 items-start\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: -30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"User Prompt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm text-gray-300\",\n                                                        children: \"Your request enters RouKey\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"bg-white border-2 \".concat(currentExample.borderColor, \" rounded-xl p-6 relative \").concat(currentExample.bgColor, \" shadow-lg \").concat(currentExample.glowColor),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-gray-300 opacity-30 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm text-gray-600 mb-3\",\n                                                                children: \"Incoming Request\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-black font-medium mb-4 text-lg leading-relaxed transition-all duration-500\",\n                                                                children: [\n                                                                    '\"',\n                                                                    currentExample.prompt,\n                                                                    '\"'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"flex items-center text-sm text-gray-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-2 h-2 bg-[#ff6b35] rounded-full mr-2 animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    \"Analyzing prompt...\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 280,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 15\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.3,\n                                            delay: 0.1\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"Intelligent Role Classification\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm text-gray-300\",\n                                                        children: \"AI analyzes & routes to optimal model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"bg-white border-2 border-gray-200 rounded-xl p-6 relative shadow-xl\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-[#ff6b35] opacity-40 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"relative z-10 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-orange-500/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 309,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm text-gray-600\",\n                                                                        children: \"RouKey Smart Classifier\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 311,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2___default().createElement(currentExample.icon, {\n                                                                        className: \"h-6 w-6 \".concat(currentExample.color, \" mx-auto mb-2\")\n                                                                    }),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Classified as\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 319,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 320,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            \"Role ID: \",\n                                                                            currentExample.role\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"bg-gray-100 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-[#ff6b35] mb-1\",\n                                                                                children: \"✓ Context Analysis\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 331,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-600\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"bg-gray-100 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-[#ff6b35] mb-1\",\n                                                                                children: \"✓ Role Matching\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 335,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-600\",\n                                                                                children: \"Complete\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 336,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 305,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 344,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 351,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.2s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-orange-500/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b35] to-transparent opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 359,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute right-0 top-1/2 transform translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-transparent opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-white to-[#ff6b35] current-flow shadow-lg shadow-orange-500/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-white current-flow-delayed shadow-sm shadow-white/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            x: 30\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            x: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.3,\n                                            delay: 0.2\n                                        },\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-xl font-semibold text-white mb-2\",\n                                                        children: \"Optimal Model Selection\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm text-gray-300\",\n                                                        children: \"Routed to the perfect model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"bg-white border-2 border-[#ff6b35]/30 rounded-xl p-6 relative shadow-xl shadow-[#ff6b35]/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 rounded-xl border-2 border-dashed border-[#ff6b35] opacity-50 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"relative z-10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-lg shadow-[#ff6b35]/30\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-lg font-bold text-black mb-1\",\n                                                                        children: currentExample.model\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm text-[#ff6b35] font-medium\",\n                                                                        children: currentExample.provider\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"\".concat(currentExample.bgColor, \" \").concat(currentExample.borderColor, \" border-2 rounded-lg p-4 text-center mb-4 shadow-lg \").concat(currentExample.glowColor),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs text-gray-400 mb-1\",\n                                                                        children: \"Assigned Role\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"font-semibold text-sm \".concat(currentExample.color),\n                                                                        children: currentExample.roleName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 405,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"grid grid-cols-2 gap-2 text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"bg-gray-100 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-[#ff6b35] mb-1\",\n                                                                                children: \"⚡ Speed\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 413,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-black font-medium\",\n                                                                                children: \"Optimal\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 412,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"bg-gray-100 rounded p-2 text-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-[#ff6b35] mb-1\",\n                                                                                children: \"\\uD83C\\uDFAF Accuracy\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 417,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-black font-medium\",\n                                                                                children: \"High\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 418,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"mt-4 flex items-center justify-center text-[#ff6b35]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 424,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm font-medium\",\n                                                                        children: \"Route Established\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 423,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.8s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-4 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-[#ff6b35]/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.6s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-6 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-[#ff6b35]/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        animationDelay: '0.4s'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"w-1 h-8 bg-[#ff6b35] rounded-full animate-pulse shadow-lg shadow-[#ff6b35]/50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 hidden lg:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"relative w-16 h-px overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-[#ff6b35] to-[#ff6b35] opacity-40\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-0 left-0 w-8 h-px bg-gradient-to-r from-transparent via-black to-[#ff6b35] current-flow shadow-lg shadow-[#ff6b35]/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 443,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-0 left-0 w-4 h-px bg-black current-flow-delayed shadow-sm shadow-black/50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"flex justify-center mt-12 space-x-2\",\n                                children: routingExamples.map((example, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveExample(index),\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-3 h-3 rounded-full transition-all duration-200 \".concat(index === activeExample ? 'bg-[#ff6b35] scale-125' : 'bg-gray-500 hover:bg-gray-400')\n                                    }, example.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 15\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mt-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#ff6b35]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Role-Based Classification\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-300\",\n                                        children: \"RouKey Smart Classifier analyzes context and classifies requests into 15+ specialized roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#ff6b35]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Smart Model Matching\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-300\",\n                                        children: \"Each role routes to your pre-configured optimal model for maximum performance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#ff6b35]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Contextual Continuity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-300\",\n                                        children: \"Maintains conversation context and role consistency across multi-turn interactions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-12 h-12 bg-[#ff6b35]/20 rounded-lg flex items-center justify-center mx-auto mb-4 shadow-lg shadow-orange-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BoltIcon_ChartBarIcon_CheckIcon_CodeBracketIcon_CpuChipIcon_PencilIcon_ShieldCheckIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-[#ff6b35]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 500,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-lg font-semibold text-white mb-2\",\n                                        children: \"Fallback Protection\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-300\",\n                                        children: \"Automatic fallback to default general chat model when no role match is found\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"mt-24 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.h4, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 15\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        className: \"text-4xl font-bold text-white mb-6\",\n                                        children: \"15+ Built-in Role Types\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.p, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 15\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            duration: 0.3,\n                                            delay: 0.05\n                                        },\n                                        className: \"text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                        children: [\n                                            \"Each role is intelligently optimized for specific use cases, with the power to create\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-[#ff6b35] font-semibold\",\n                                                children: \" custom roles\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 534,\n                                                columnNumber: 15\n                                            }, this),\n                                            \" for your unique workflows\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 526,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 516,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"relative max-w-6xl mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-[#ff6b35]/5 via-transparent to-[#f7931e]/5 rounded-3xl blur-3xl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 pointer-events-none\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            viewBox: \"0 0 800 600\",\n                                            className: \"jsx-34e24bce47f966\" + \" \" + \"w-full h-full opacity-20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                                                    className: \"jsx-34e24bce47f966\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                                                        id: \"connectionGradient\",\n                                                        x1: \"0%\",\n                                                        y1: \"0%\",\n                                                        x2: \"100%\",\n                                                        y2: \"0%\",\n                                                        className: \"jsx-34e24bce47f966\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"0%\",\n                                                                stopColor: \"#ff6b35\",\n                                                                stopOpacity: \"0.3\",\n                                                                className: \"jsx-34e24bce47f966\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"50%\",\n                                                                stopColor: \"#f7931e\",\n                                                                stopOpacity: \"0.6\",\n                                                                className: \"jsx-34e24bce47f966\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                                                offset: \"100%\",\n                                                                stopColor: \"#ff6b35\",\n                                                                stopOpacity: \"0.3\",\n                                                                className: \"jsx-34e24bce47f966\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.path, {\n                                                    d: \"M100,150 Q400,50 700,150\",\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"1\",\n                                                    fill: \"none\",\n                                                    initial: {\n                                                        pathLength: 0\n                                                    },\n                                                    animate: {\n                                                        pathLength: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        repeatType: \"loop\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.path, {\n                                                    d: \"M100,300 Q400,200 700,300\",\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"1\",\n                                                    fill: \"none\",\n                                                    initial: {\n                                                        pathLength: 0\n                                                    },\n                                                    animate: {\n                                                        pathLength: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        repeatType: \"loop\",\n                                                        delay: 1\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.path, {\n                                                    d: \"M100,450 Q400,350 700,450\",\n                                                    stroke: \"url(#connectionGradient)\",\n                                                    strokeWidth: \"1\",\n                                                    fill: \"none\",\n                                                    initial: {\n                                                        pathLength: 0\n                                                    },\n                                                    animate: {\n                                                        pathLength: 1\n                                                    },\n                                                    transition: {\n                                                        duration: 3,\n                                                        repeat: Infinity,\n                                                        repeatType: \"loop\",\n                                                        delay: 2\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 544,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"relative grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            [\n                                                {\n                                                    name: 'General Chat',\n                                                    icon: '💬',\n                                                    category: 'communication',\n                                                    description: 'Everyday conversations',\n                                                    delay: 0\n                                                },\n                                                {\n                                                    name: 'Logic & Reasoning',\n                                                    icon: '🧠',\n                                                    category: 'featured',\n                                                    description: 'Complex problem solving',\n                                                    delay: 0.1\n                                                },\n                                                {\n                                                    name: 'Writing & Content',\n                                                    icon: '✍️',\n                                                    category: 'creative',\n                                                    description: 'Content creation & editing',\n                                                    delay: 0.2\n                                                },\n                                                {\n                                                    name: 'Frontend Coding',\n                                                    icon: '⚛️',\n                                                    category: 'featured',\n                                                    description: 'UI/UX development',\n                                                    delay: 0.3\n                                                },\n                                                {\n                                                    name: 'Backend Coding',\n                                                    icon: '⚙️',\n                                                    category: 'technical',\n                                                    description: 'Server & API development',\n                                                    delay: 0.4\n                                                },\n                                                {\n                                                    name: 'Research & Analysis',\n                                                    icon: '🔬',\n                                                    category: 'featured',\n                                                    description: 'Data insights & research',\n                                                    delay: 0.5\n                                                },\n                                                {\n                                                    name: 'Summarization',\n                                                    icon: '📝',\n                                                    category: 'productivity',\n                                                    description: 'Content summarization',\n                                                    delay: 0.6\n                                                },\n                                                {\n                                                    name: 'Translation',\n                                                    icon: '🌐',\n                                                    category: 'featured',\n                                                    description: 'Multi-language support',\n                                                    delay: 0.7\n                                                },\n                                                {\n                                                    name: 'Data Extraction',\n                                                    icon: '📊',\n                                                    category: 'technical',\n                                                    description: 'Information extraction',\n                                                    delay: 0.8\n                                                },\n                                                {\n                                                    name: 'Brainstorming',\n                                                    icon: '💡',\n                                                    category: 'featured',\n                                                    description: 'Idea generation',\n                                                    delay: 0.9\n                                                },\n                                                {\n                                                    name: 'Education',\n                                                    icon: '🎓',\n                                                    category: 'learning',\n                                                    description: 'Teaching & explanations',\n                                                    delay: 1.0\n                                                },\n                                                {\n                                                    name: 'Audio Transcription',\n                                                    icon: '🎵',\n                                                    category: 'featured',\n                                                    description: 'Speech to text',\n                                                    delay: 1.1\n                                                }\n                                            ].map((role, index)=>{\n                                                const isFeatured = role.category === 'featured';\n                                                const cardStyles = isFeatured ? 'bg-gradient-to-br from-[#ff6b35] via-[#ff7043] to-[#f7931e] text-white border-[#ff6b35]/30 shadow-orange-500/25' : 'bg-white text-gray-900 border-gray-200 shadow-gray-900/10';\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        scale: 0.8,\n                                                        y: 20\n                                                    },\n                                                    whileInView: {\n                                                        opacity: 1,\n                                                        scale: 1,\n                                                        y: 0\n                                                    },\n                                                    viewport: {\n                                                        once: true\n                                                    },\n                                                    transition: {\n                                                        delay: role.delay,\n                                                        duration: 0.5\n                                                    },\n                                                    className: \"group relative\",\n                                                    children: [\n                                                        isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute -inset-1 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl blur-sm opacity-30 group-hover:opacity-60 transition-opacity duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 688,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"relative \".concat(cardStyles, \" rounded-2xl p-6 border-2 transition-all duration-300 shadow-xl backdrop-blur-sm overflow-hidden\"),\n                                                            children: [\n                                                                isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 opacity-10\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            backgroundImage: \"\\n                                radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.2) 1px, transparent 1px),\\n                                radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.2) 1px, transparent 1px),\\n                                radial-gradient(circle at 40% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                              \",\n                                                                            backgroundSize: '30px 30px, 25px 25px, 35px 35px',\n                                                                            animation: 'float 6s ease-in-out infinite'\n                                                                        },\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 694,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                !isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 opacity-5\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            backgroundImage: \"\\n                                linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),\\n                                linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px)\\n                              \",\n                                                                            backgroundSize: '20px 20px'\n                                                                        },\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"relative z-10 text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"relative mb-4\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"text-5xl filter \".concat(isFeatured ? 'drop-shadow-lg' : 'drop-shadow-md'),\n                                                                                    children: role.icon\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                    lineNumber: 729,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 pointer-events-none\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-2 right-2 w-1 h-1 bg-white/60 rounded-full animate-ping\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                            lineNumber: 735,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            style: {\n                                                                                                animationDelay: '1s'\n                                                                                            },\n                                                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"absolute bottom-2 left-2 w-1 h-1 bg-white/40 rounded-full animate-ping\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                            lineNumber: 736,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                    lineNumber: 734,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 728,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"font-bold text-sm mb-2 leading-tight \".concat(isFeatured ? 'text-white' : 'text-gray-900'),\n                                                                            children: role.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 742,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs leading-relaxed \".concat(isFeatured ? 'text-white/80' : 'text-gray-600'),\n                                                                            children: role.description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 726,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-3 right-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-2 h-2 bg-white/80 rounded-full animate-pulse\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 756,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                            lineNumber: 691,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, role.name, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8,\n                                                    y: 20\n                                                },\n                                                whileInView: {\n                                                    opacity: 1,\n                                                    scale: 1,\n                                                    y: 0\n                                                },\n                                                viewport: {\n                                                    once: true\n                                                },\n                                                transition: {\n                                                    delay: 1.2,\n                                                    duration: 0.5\n                                                },\n                                                className: \"group relative col-span-2 md:col-span-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute -inset-1 bg-gradient-to-r from-[#ff6b35] via-[#ff7043] to-[#f7931e] rounded-2xl blur-sm opacity-50 transition-opacity duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"relative bg-gradient-to-br from-[#ff6b35] via-[#ff7043] to-[#f7931e] rounded-2xl p-6 border-2 border-dashed border-white/40 transition-all duration-300 shadow-2xl shadow-orange-500/30 overflow-hidden\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 opacity-15\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        backgroundImage: \"\\n                          radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.3) 2px, transparent 2px),\\n                          radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.2) 1px, transparent 1px),\\n                          linear-gradient(45deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px),\\n                          linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)\\n                        \",\n                                                                        backgroundSize: '30px 30px, 20px 20px, 15px 15px, 15px 15px',\n                                                                        animation: 'float 8s ease-in-out infinite, grid-move 12s linear infinite'\n                                                                    },\n                                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                    lineNumber: 779,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 778,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 pointer-events-none\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-4 right-6 w-1.5 h-1.5 bg-white/70 rounded-full animate-ping\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 796,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            animationDelay: '1s'\n                                                                        },\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute bottom-6 left-4 w-1 h-1 bg-white/50 rounded-full animate-ping\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            animationDelay: '2s'\n                                                                        },\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-1/2 left-1/2 w-0.5 h-0.5 bg-white/60 rounded-full animate-ping\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 798,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 795,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"relative z-10 text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"relative mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-5xl filter drop-shadow-2xl\",\n                                                                                children: \"\\uD83C\\uDFAF\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 804,\n                                                                                columnNumber: 23\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 text-5xl filter blur-sm opacity-50\",\n                                                                                children: \"\\uD83C\\uDFAF\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                                lineNumber: 808,\n                                                                                columnNumber: 23\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 803,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-white font-bold text-sm mb-2 drop-shadow-lg\",\n                                                                        children: \"Custom Roles\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 813,\n                                                                        columnNumber: 21\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-white/90 text-xs leading-relaxed\",\n                                                                        children: \"Create your own specialized roles\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                        lineNumber: 816,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 801,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute inset-0 rounded-2xl border-2 border-white/60 animate-pulse\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute top-2 left-2 w-3 h-3 border-l-2 border-t-2 border-white/60 rounded-tl-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 825,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"absolute bottom-2 right-2 w-3 h-3 border-r-2 border-b-2 border-white/60 rounded-br-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 826,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        whileInView: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        viewport: {\n                                            once: true\n                                        },\n                                        transition: {\n                                            delay: 1.5\n                                        },\n                                        className: \"text-center mt-12\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-34e24bce47f966\" + \" \" + \"inline-flex items-center bg-black backdrop-blur-sm rounded-full px-8 py-4 border border-gray-800\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"flex items-center space-x-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-2xl font-bold text-white\",\n                                                                children: \"15+\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 842,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs text-gray-400\",\n                                                                children: \"Built-in Roles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 843,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-px h-8 bg-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-2xl font-bold text-[#ff6b35]\",\n                                                                children: \"∞\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 847,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs text-gray-400\",\n                                                                children: \"Custom Roles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 848,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"w-px h-8 bg-gray-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-2xl font-bold text-white\",\n                                                                children: \"100%\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 852,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs text-gray-400\",\n                                                                children: \"Accuracy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                                lineNumber: 853,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                            lineNumber: 839,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 832,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        className: \"mt-20 bg-black rounded-2xl p-8 border border-gray-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-2xl font-bold text-white mb-4\",\n                                        children: \"Create Custom Roles for Your Workflow\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 870,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-400 max-w-3xl mx-auto\",\n                                        children: \"Beyond our 15+ built-in roles, RouKey empowers you to create custom roles tailored to your specific needs. Define role patterns, assign optimal models, and let our AI classifier automatically route requests.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 869,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-orange-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"text-2xl text-white font-bold\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 881,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-lg font-semibold text-white mb-3\",\n                                                children: \"Define Role Pattern\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 883,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-400 text-sm leading-relaxed\",\n                                                children: \"Describe the types of requests this role should handle. Our AI learns from your examples and keywords.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 884,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Example Pattern:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 888,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm text-[#ff6b35] font-mono\",\n                                                        children: '\"SQL queries, database optimization, schema design\"'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 889,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 887,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 879,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-gray-700 to-gray-800 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-gray-500/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"text-2xl text-white font-bold\",\n                                                    children: \"2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 898,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 897,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-lg font-semibold text-white mb-3\",\n                                                children: \"Assign Optimal Model\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 900,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-400 text-sm leading-relaxed\",\n                                                children: \"Choose which model performs best for this role from your available providers and configurations.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Model Selection:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 905,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm text-white font-medium\",\n                                                        children: \"Claude 3.5 Sonnet → Database Expert\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 906,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"w-16 h-16 bg-gradient-to-br from-[#f7931e] to-[#ff6b35] rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg shadow-orange-400/30\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-34e24bce47f966\" + \" \" + \"text-2xl text-white font-bold\",\n                                                    children: \"3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                    lineNumber: 915,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-lg font-semibold text-white mb-3\",\n                                                children: \"Automatic Routing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 917,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-gray-400 text-sm leading-relaxed\",\n                                                children: \"Our RouKey Smart Classifier automatically detects and routes matching requests to your custom role.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 918,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-34e24bce47f966\" + \" \" + \"mt-4 bg-gray-800 rounded-lg p-3 text-left border border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-xs text-gray-500 mb-1\",\n                                                        children: \"Auto-Detection:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 922,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-34e24bce47f966\" + \" \" + \"text-sm text-[#f7931e]\",\n                                                        children: \"✓ Pattern Recognition Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                        lineNumber: 923,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 877,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-34e24bce47f966\" + \" \" + \"text-center mt-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-34e24bce47f966\" + \" \" + \"inline-flex items-center bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-lg px-6 py-3 text-white font-medium shadow-lg shadow-orange-500/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-34e24bce47f966\",\n                                        children: \"\\uD83D\\uDE80 Start Creating Custom Roles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                    lineNumber: 931,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                                lineNumber: 930,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\RoutingVisualization.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingVisualization, \"SlEVdab/r4hG20wt70ej7grkDC0=\");\n_c = RoutingVisualization;\nvar _c;\n$RefreshReg$(_c, \"RoutingVisualization\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\n"));

/***/ })

});