"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx":
/*!**********************************************************!*\
  !*** ./src/components/landing/AIIntegrationsSection.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIIntegrationsSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst aiProviders = [\n    // Row 1 - Core providers with verified working logos from LobeHub\n    {\n        name: 'OpenAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/openai.png',\n        alt: 'OpenAI GPT Models'\n    },\n    {\n        name: 'Anthropic',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/anthropic.png',\n        alt: 'Anthropic Claude'\n    },\n    {\n        name: 'Google',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/gemini-color.png',\n        alt: 'Google Gemini'\n    },\n    {\n        name: 'Meta',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/meta-color.png',\n        alt: 'Meta Llama Models'\n    },\n    {\n        name: 'Mistral AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/mistral-color.png',\n        alt: 'Mistral AI'\n    },\n    {\n        name: 'DeepSeek',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/deepseek-color.png',\n        alt: 'DeepSeek'\n    },\n    {\n        name: 'Microsoft',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/microsoft-color.png',\n        alt: 'Microsoft Azure'\n    },\n    {\n        name: 'Cohere',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/cohere-color.png',\n        alt: 'Cohere'\n    },\n    {\n        name: 'Perplexity',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/perplexity-color.png',\n        alt: 'Perplexity'\n    },\n    {\n        name: 'xAI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/xai.png',\n        alt: 'xAI Grok'\n    },\n    {\n        name: 'NVIDIA',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/nvidia-color.png',\n        alt: 'NVIDIA'\n    },\n    {\n        name: 'Replicate',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/replicate.png',\n        alt: 'Replicate'\n    },\n    // Row 2 - Additional verified providers from LobeHub\n    {\n        name: 'AI21 Labs',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/ai21.png',\n        alt: 'AI21 Labs'\n    },\n    {\n        name: 'Amazon',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/bedrock-color.png',\n        alt: 'Amazon Bedrock'\n    },\n    {\n        name: 'Alibaba',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/alibaba-color.png',\n        alt: 'Alibaba Qwen Models'\n    },\n    {\n        name: 'Zhipu AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/chatglm-color.png',\n        alt: 'Zhipu AI GLM'\n    },\n    {\n        name: 'Yi AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/yi-color.png',\n        alt: 'Yi AI'\n    },\n    {\n        name: 'Hugging Face',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/huggingface-color.png',\n        alt: 'Hugging Face'\n    },\n    {\n        name: 'Moonshot',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/moonshot.png',\n        alt: 'Moonshot AI'\n    },\n    {\n        name: 'Baidu',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baidu-color.png',\n        alt: 'Baidu Wenxin'\n    },\n    {\n        name: 'ByteDance',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/doubao-color.png',\n        alt: 'ByteDance Doubao'\n    },\n    {\n        name: 'Minimax',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/minimax-color.png',\n        alt: 'Minimax'\n    },\n    {\n        name: 'Baichuan',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baichuan-color.png',\n        alt: 'Baichuan AI'\n    },\n    {\n        name: 'Together AI',\n        logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/together-color.png',\n        alt: 'Together AI'\n    }\n];\n// Custom hook for scroll-based animations\nfunction useScrollAnimation() {\n    _s();\n    const [scrollY, setScrollY] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useScrollAnimation.useEffect\": ()=>{\n            const handleScroll = {\n                \"useScrollAnimation.useEffect.handleScroll\": ()=>{\n                    setScrollY(window.scrollY);\n                }\n            }[\"useScrollAnimation.useEffect.handleScroll\"];\n            const observer = new IntersectionObserver({\n                \"useScrollAnimation.useEffect\": (param)=>{\n                    let [entry] = param;\n                    setIsVisible(entry.isIntersecting);\n                }\n            }[\"useScrollAnimation.useEffect\"], {\n                threshold: 0.1\n            });\n            if (ref.current) {\n                observer.observe(ref.current);\n            }\n            window.addEventListener('scroll', handleScroll, {\n                passive: true\n            });\n            return ({\n                \"useScrollAnimation.useEffect\": ()=>{\n                    window.removeEventListener('scroll', handleScroll);\n                    if (ref.current) {\n                        observer.unobserve(ref.current);\n                    }\n                }\n            })[\"useScrollAnimation.useEffect\"];\n        }\n    }[\"useScrollAnimation.useEffect\"], []);\n    return {\n        scrollY,\n        isVisible,\n        ref\n    };\n}\n_s(useScrollAnimation, \"nzxjuC2Kq5Ot1yzB0Kyf6EQhEic=\");\nfunction AIIntegrationsSection() {\n    _s1();\n    const { scrollY, isVisible, ref } = useScrollAnimation();\n    // Calculate transform values based on scroll position\n    const getTransform = function(index) {\n        let isSecondRow = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (!isVisible) return {};\n        const baseOffset = scrollY * 0.1;\n        const rowMultiplier = isSecondRow ? -1 : 1;\n        const itemOffset = index % 6 * 2; // Stagger effect\n        const translateY = baseOffset * rowMultiplier + itemOffset;\n        const rotateZ = Math.sin(scrollY * 0.01 + index) * 2; // Subtle rotation\n        const scale = 1 + Math.sin(scrollY * 0.005 + index) * 0.02; // Subtle scale\n        return {\n            transform: \"translateY(\".concat(translateY, \"px) rotateZ(\").concat(rotateZ, \"deg) scale(\").concat(scale, \")\"),\n            transition: 'transform 0.1s ease-out'\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative py-20 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                style: {\n                    background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundImage: \"\\n              linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),\\n              linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)\\n            \",\n                        backgroundSize: '50px 50px'\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6\n                        },\n                        className: \"text-center mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n                                children: [\n                                    \"Connect to any AI model with\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]\",\n                                        children: \"300+ integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-300 max-w-3xl mx-auto\",\n                                children: \"RouKey provides unified access to every major AI provider through a single API. No vendor lock-in, just seamless AI integration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.2\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-8\",\n                        children: aiProviders.slice(0, 12).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.4\n                        },\n                        className: \"grid grid-cols-6 md:grid-cols-12 gap-4 mb-12\",\n                        children: aiProviders.slice(12, 24).map((provider, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.8\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                transition: {\n                                    duration: 0.4,\n                                    delay: index * 0.05\n                                },\n                                className: \"group relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                src: provider.logo,\n                                                alt: provider.alt,\n                                                width: 32,\n                                                height: 32,\n                                                className: \"w-full h-full object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap\",\n                                        children: provider.alt\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, provider.name, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        transition: {\n                            duration: 0.6,\n                            delay: 0.6\n                        },\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mb-6\",\n                                children: \"And 270+ more providers available through RouKey's unified API\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105\",\n                                        children: \"View All Integrations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"px-8 py-3 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300\",\n                                        children: \"Start Free Trial\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\AIIntegrationsSection.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s1(AIIntegrationsSection, \"L7HRw6zfXW83el553UQNuEdZsTc=\", false, function() {\n    return [\n        useScrollAnimation\n    ];\n});\n_c = AIIntegrationsSection;\nvar _c;\n$RefreshReg$(_c, \"AIIntegrationsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/AIIntegrationsSection.tsx\n"));

/***/ })

});