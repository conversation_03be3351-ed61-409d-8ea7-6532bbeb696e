'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { useEffect, useRef, useState } from 'react';

const aiProviders = [
  // Row 1 - Core providers with verified working logos from LobeHub
  { name: 'OpenAI', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/openai.png', alt: 'OpenAI GPT Models' },
  { name: 'Anthropic', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/anthropic.png', alt: 'Anthropic Claude' },
  { name: 'Google', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/gemini-color.png', alt: 'Google Gemini' },
  { name: 'Meta', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/meta-color.png', alt: 'Meta Llama Models' },
  { name: 'Mistral AI', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/mistral-color.png', alt: 'Mistral AI' },
  { name: 'DeepSeek', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/deepseek-color.png', alt: 'DeepSeek' },
  { name: 'Microsoft', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/microsoft-color.png', alt: 'Microsoft Azure' },
  { name: 'Cohere', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/cohere-color.png', alt: 'Cohere' },
  { name: 'Perplexity', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/perplexity-color.png', alt: 'Perplexity' },
  { name: 'xAI', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/xai.png', alt: 'xAI Grok' },
  { name: 'NVIDIA', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/nvidia-color.png', alt: 'NVIDIA' },
  { name: 'Replicate', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/replicate.png', alt: 'Replicate' },

  // Row 2 - Additional verified providers from LobeHub
  { name: 'AI21 Labs', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/ai21.png', alt: 'AI21 Labs' },
  { name: 'Amazon', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/bedrock-color.png', alt: 'Amazon Bedrock' },
  { name: 'Alibaba', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/alibaba-color.png', alt: 'Alibaba Qwen Models' },
  { name: 'Zhipu AI', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/chatglm-color.png', alt: 'Zhipu AI GLM' },
  { name: 'Yi AI', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/yi-color.png', alt: 'Yi AI' },
  { name: 'Hugging Face', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/huggingface-color.png', alt: 'Hugging Face' },
  { name: 'Moonshot', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/moonshot.png', alt: 'Moonshot AI' },
  { name: 'Baidu', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baidu-color.png', alt: 'Baidu Wenxin' },
  { name: 'ByteDance', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/doubao-color.png', alt: 'ByteDance Doubao' },
  { name: 'Minimax', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/minimax-color.png', alt: 'Minimax' },
  { name: 'Baichuan', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/baichuan-color.png', alt: 'Baichuan AI' },
  { name: 'Together AI', logo: 'https://registry.npmmirror.com/@lobehub/icons-static-png/latest/files/light/together-color.png', alt: 'Together AI' }
];

export default function AIIntegrationsSection() {
  const ref = useRef<HTMLDivElement>(null);

  return (
    <>
      {/* CSS Animations for infinite scrolling */}
      <style jsx>{`
        @keyframes scroll-right {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }

        @keyframes scroll-left {
          0% {
            transform: translateX(-50%);
          }
          100% {
            transform: translateX(0);
          }
        }

        .scroll-right {
          animation: scroll-right 20s linear infinite;
        }

        .scroll-left {
          animation: scroll-left 20s linear infinite;
        }

        .scroll-container {
          display: flex;
          width: 200%;
        }

        .scroll-track {
          display: flex;
          width: 50%;
          gap: 1rem;
          flex-shrink: 0;
        }
      `}</style>

      <section ref={ref} className="relative py-20 overflow-hidden">
      {/* Background with same gradient as main page */}
      <div 
        className="absolute inset-0"
        style={{
          background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
        }}
      />
      
      {/* Grid overlay */}
      <div className="absolute inset-0 opacity-20">
        <div
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px'
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Connect to any AI model with{' '}
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
              300+ integrations
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            RouKey provides unified access to every major AI provider through a single API. 
            No vendor lock-in, just seamless AI integration.
          </p>
        </motion.div>

        {/* AI Provider Logos Grid - Scrolling Right */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="relative mb-8 overflow-hidden"
        >
          <div className="scroll-container">
            <div className="scroll-track scroll-right">
              {/* First set of logos */}
              {aiProviders.slice(0, 12).map((provider, index) => (
                <div
                  key={`first-${provider.name}`}
                  className="group relative flex-shrink-0"
                >
                  <div className="w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110">
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1">
                      <Image
                        src={provider.logo}
                        alt={provider.alt}
                        width={32}
                        height={32}
                        className="w-full h-full object-contain"
                      />
                    </div>
                  </div>

                  {/* Tooltip */}
                  <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    {provider.alt}
                  </div>
                </div>
              ))}
            </div>
            <div className="scroll-track scroll-right">
              {/* Duplicate set for seamless loop */}
              {aiProviders.slice(0, 12).map((provider, index) => (
                <div
                  key={`second-${provider.name}`}
                  className="group relative flex-shrink-0"
                >
                  <div className="w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110">
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1">
                      <Image
                        src={provider.logo}
                        alt={provider.alt}
                        width={32}
                        height={32}
                        className="w-full h-full object-contain"
                      />
                    </div>
                  </div>

                  {/* Tooltip */}
                  <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    {provider.alt}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Second Row - Scrolling Left */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="relative mb-12 overflow-hidden"
        >
          <div className="scroll-container">
            <div className="scroll-track scroll-left">
              {/* First set of logos */}
              {aiProviders.slice(12, 24).map((provider, index) => (
                <div
                  key={`first-${provider.name}`}
                  className="group relative flex-shrink-0"
                >
                  <div className="w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110">
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1">
                      <Image
                        src={provider.logo}
                        alt={provider.alt}
                        width={32}
                        height={32}
                        className="w-full h-full object-contain"
                      />
                    </div>
                  </div>

                  {/* Tooltip */}
                  <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    {provider.alt}
                  </div>
                </div>
              ))}
            </div>
            <div className="scroll-track scroll-left">
              {/* Duplicate set for seamless loop */}
              {aiProviders.slice(12, 24).map((provider, index) => (
                <div
                  key={`second-${provider.name}`}
                  className="group relative flex-shrink-0"
                >
                  <div className="w-12 h-12 md:w-16 md:h-16 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 flex items-center justify-center hover:bg-white/20 transition-all duration-300 hover:scale-110">
                    <div className="w-8 h-8 md:w-10 md:h-10 bg-white rounded-lg flex items-center justify-center p-1">
                      <Image
                        src={provider.logo}
                        alt={provider.alt}
                        width={32}
                        height={32}
                        className="w-full h-full object-contain"
                      />
                    </div>
                  </div>

                  {/* Tooltip */}
                  <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap z-10">
                    {provider.alt}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center"
        >
          <p className="text-gray-400 mb-6">
            And 270+ more providers available through RouKey's unified API
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="px-8 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-[#ff6b35]/25 transition-all duration-300 hover:scale-105">
              View All Integrations
            </button>
            <button className="px-8 py-3 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300">
              Start Free Trial
            </button>
          </div>
        </motion.div>
      </div>
    </section>
    </>
  );
}
